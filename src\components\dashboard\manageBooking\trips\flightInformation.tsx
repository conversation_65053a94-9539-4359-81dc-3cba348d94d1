import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Plane, User, ChevronRight, ChevronDown } from "lucide-react";

import axios from "axios";
import { useCustomSession } from "@/hooks/use-custom-session";
import FlightDetails from "./flightDetails";
import <PERSON><PERSON> from "react-lottie";
import animation from "../../../../../public/Hero section banner.json";

interface FlightSegment {
  id: string;
  departure_time: string;
  arrival_time: string;
  departure_airport: string;
  arrival_airport: string;
  departure_city: string;
  arrival_city: string;
  departure_code: string;
  arrival_code: string;
  airline: string;
  flight_number: string;
  aircraft: string;
  duration: string;
  departure_date: string;
  arrival_date: string;
}

interface Passenger {
  id: string;
  name: string;
  type: "adult" | "child" | "infant";
  seat?: string;
  frequent_flyer?: string;
  special_assistance?: string;
  meal_preference?: string;
  avatarUrl?: string; // Added for placeholder image
}

interface FlightInformationProps {
  // Trip Header Info
  tripTitle?: string;
  tripDate?: string;
  bookingReference?: string;
  title?: string;
  date?: string;
  id?: string;
  status?: string;

  // Flight Details
  segments?: FlightSegment[];

  // Booking Info
  bookingDate?: string;

  // Passenger Info
  passengers?: Passenger[];

  // Contact Info
  contactSupport?: {
    chat: boolean;
    email: boolean;
  };

  // Additional Options
  showDownloadTicket?: boolean;
  showFlightChanges?: boolean;
  showCancelFlight?: boolean;
  flightDetails?: { tf_reference: string };
  handleBackToTrips?: () => void;
}

const FlightInformation: React.FC<FlightInformationProps> = ({ ...props }) => {
  // console.log("FlightInformation props:", props);
  const [passengerOpen, setPassengerOpen] = useState(true);
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showAllInfo, setShowAllInfo] = useState(false);

  // Call booking details API on mount
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;
  React.useEffect(() => {
    const fetchBookingDetails = async () => {
      try {
        // console.log("Booking details request body:", {
        //   tf_booking_reference: props.flightDetails?.tf_reference,
        //   request_uuid: "Llnto_Ts-AHm0tc5h-vqyEqMr3IJn8jyoryCAyuT",
        // });
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/flight/booking-details`,
          {
            tf_booking_reference: props.flightDetails?.tf_reference,
            request_uuid: "Llnto_Ts-AHm0tc5h-vqyEqMr3IJn8jyoryCAyuT", // Replace with dynamic value if needed
          },
          {
            headers: {
              Authorization: `Bearer ${session?.accessToken || token}`,
              Accept: "application/json",
            },
          }
        );
        setBookingDetails(response.data.detail.data);
        // console.log("Booking Details API response:", response.data.detail.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          console.error("Backend error:", error.response.data);
        } else {
          console.error("Booking Details API error:", error);
        }
      } finally {
        setLoading(false);
      }
    };
    fetchBookingDetails();
  }, []);

  const handleDownloadPDF = async () => {
    if (!bookingDetails?.pdf_url) return;
    try {
      const response = await fetch(bookingDetails.pdf_url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${session?.accessToken || token}`,
        },
      });
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "ticket.pdf");
      document.body.appendChild(link);
      link.click();
      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
      window.URL.revokeObjectURL(url);
    } catch (error) {
      // Fallback: open in new tab if download fails
      window.open(bookingDetails.pdf_url, "_blank");
    }
  };

  const handleEmailClick = () => {
    window.location.href =
      "mailto:<EMAIL>?subject=Support Inquiry&body=Hello,";
  };

  const handleWhatsAppClick = () => {
    window.open("https://wa.me/447984954831", "_blank");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <svg
            className="animate-spin h-8 w-8 text-brand-info mb-2"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v8z"
            ></path>
          </svg>
          <span className="text-brand-info font-semibold">
            Loading booking details...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header Section - Dark Navy Blue */}
      <div className="bg-[#080236] text-white px-6 relative rounded-[16px]">
        {/* Background pattern/decoration */}
        <div className="relative z-10 max-w-6xl mx-auto flex items-center justify-between">
          <div className=" py-8">
            <p className="text-sm text-white/80 mb-1">Your Upcoming Trip</p>
            <h1 className="text-2xl font-bold mb-3">{props.title}</h1>
            <p className="text-sm text-white/90 mb-1">{props.date}</p>
            <p className="text-sm text-white/90">
              {props.id
                ? `Booking Reference: ${bookingDetails?.tf_reference}`
                : ""}
            </p>
            <p className="text-sm text-white/90">{props.status}</p>
          </div>
          {/* <div className=" absolute right-0 bottom-0"> */}
          <img
            src="images/dashboard/iconImage/BACKGROUND_2.png"
            alt="Hero"
            className="hidden md:block w-1/2 h-[220px] object-fill"
          />

          {/* </div> */}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Back Button */}
        <button
          className="flex items-center text-brand-grey font-semibold transition-colors mb-6"
          onClick={props.handleBackToTrips}
        >
          <ChevronRight className="w-4 h-4 rotate-180 mr-1 font-semibold" />
          <span className="text-sm">Back to manage Booking</span>
        </button>

        {/* Main Content - Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Flight Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Flight Information Section with Tabs */}
            <h2 className="text-xl font-semibold text-brand-black mb-2">
              Flight Information
            </h2>

            {/* tabs here  */}

            <Tabs defaultValue={bookingDetails?.flights[0]?.id || "0"}>
              <TabsList className="rounded-tr-md rounded-tl-md rounded-bl-none rounded-br-none  h-6 p-0 mb-3 bg-none border-b border-brand">
                {bookingDetails?.flights?.map((flight: any, idx: number) => (
                  <TabsTrigger
                    key={flight.id || idx}
                    value={flight.id || String(idx)}
                    className="rounded-tr-lg rounded-tl-lg rounded-bl-none rounded-br-none text-sm font-medium h-6 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
                  >
                    {flight.route} {/* or any label you want */}
                  </TabsTrigger>
                ))}
              </TabsList>

              {bookingDetails?.flights?.map((flight: any, idx: number) => (
                <TabsContent
                  key={flight.id || idx}
                  value={flight.id || String(idx)}
                >
                  {/* Render your flight details here */}
                  <FlightDetails
                    flight={flight}
                    tf_reference={bookingDetails?.tf_reference}
                  />
                </TabsContent>
              ))}
            </Tabs>

            {/* Additional Sections - Full Width Below */}
            <div>
              {/* Passenger Information Card - Figma Style */}
              <div>
                {bookingDetails?.travelers.length > 0 && (
                  <h3 className="text-lg font-semibold mb-4 text-brand-black">
                    Passenger Information
                  </h3>
                )}
                {bookingDetails?.travelers?.map((item: any, index: number) => (
                  <div
                    key={index}
                    className="bg-white rounded-xl border border-gray-200 mt-6 overflow-hidden"
                  >
                    <div className="flex items-center justify-between px-6 py-4 cursor-pointer">
                      <div className="flex gap-3">
                        <User className="w-5 h-5 text-[#1E1E76]" />
                        <div>
                          <div className="flex flex-col items-start gap-2">
                            <span className="font-semibold text-gray-900">
                              {item.actual_name || "Passenger Name"}
                            </span>
                            <div className="flex gap-2 md:flex-row flex-col">
                              <span className="text-xs  text-gray-500 rounded px-0 py-0.5">
                                {props.passengers?.[0]?.type === "adult"
                                  ? "Adult"
                                  : props.passengers?.[0]?.type || "Type"}
                              </span>
                              <span className="text-xs bg-gray-100 text-gray-500 rounded px-2 py-0.5">
                                Economy Semi Flex
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-[#1E1E76] font-medium opacity-0">
                          Seat {props.passengers?.[0]?.seat || "13 A"}
                        </span>
                        <ChevronDown
                          className={`w-7 h-7 pt-[2px] text-[#1E1E76] transition-transform duration-200 border  rounded-full ${passengerOpen ? "" : "rotate-180"}`}
                          onClick={() => setPassengerOpen((open) => !open)}
                        />
                      </div>
                    </div>
                    {passengerOpen && (
                      <div className="border-t border-gray-100">
                        <div className="flex  justify-between px-6 py-4 hover:bg-gray-50 transition cursor-pointer">
                          <div className="flex h-full gap-3">
                            {/* <Luggage className="w-4 h-4 text-gray-400 mt-1" /> */}
                            <div>
                              <img
                                src="images/dashboard/iconImage/baggage.svg"
                                alt=""
                                className="w-3 text-gray-400 mt-1"
                              />
                            </div>
                            <div className="pl-2">
                              <div className="text-brand-grey">
                                Baggage Allowance
                              </div>
                              <div className="text-xs text-brand-grey">
                                Included In Fare
                              </div>
                              <div className="flex gap-3 mt-2 md:flex-row flex-col">
                                <span className="text-xs bg-gray-100 text-gray-400 rounded px-2 py-0.5">
                                  1 Hand bag
                                </span>
                                <span className="text-xs bg-gray-100 text-gray-400 rounded px-2 py-0.5">
                                  2 Check bag 23kg
                                </span>
                                <span className="text-xs bg-gray-100 text-gray-400 rounded px-2 py-0.5">
                                  1 Cabin bag
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex  gap-1 text-brand-grey font-medium text-sm">
                            Manage Baggage <ChevronRight className="w-4 h-4" />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="space-y-6 mt-6">
                {/* Other Information */}
                <h3 className="text-lg font-semibold mb-4 text-brand-black">
                  Other Information
                </h3>
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="space-y-4 text-gray-600 text-sm leading-relaxed">
                    {(() => {
                      const supplierInfo =
                        bookingDetails?.raw_data?.CommandList?.GetBookingDetails
                          ?.RouterHistory?.BookingRouter?.SupplierInfoList?.[0]
                          ?.SupplierInfo || [];
                      const nonUrlInfos = supplierInfo.filter(
                        (info: any) => info.InfoType !== "url"
                      );
                      const urlInfos = supplierInfo.filter(
                        (info: any) => info.InfoType === "url"
                      );

                      // Estimate lines based on text content (roughly 80 chars per line)
                      const estimateLines = (text: string) => {
                        if (!text) return 1;
                        return Math.max(1, Math.ceil(text.length / 80));
                      };

                      // Calculate total estimated lines
                      let totalEstimatedLines = 0;
                      nonUrlInfos.forEach((info) => {
                        totalEstimatedLines += 1; // DisplayName line
                        totalEstimatedLines += estimateLines(info.Info); // Info content lines
                      });
                      urlInfos.forEach((info) => {
                        totalEstimatedLines += 1; // URL line
                      });

                      const shouldShowReadMore = totalEstimatedLines > 7;

                      let visibleNonUrlInfos = nonUrlInfos;
                      let visibleUrlInfos = urlInfos;

                      if (!showAllInfo && shouldShowReadMore) {
                        let remainingLines = 7;
                        visibleNonUrlInfos = [];
                        visibleUrlInfos = [];

                        // First, add non-URL infos
                        for (const info of nonUrlInfos) {
                          const infoLines = 1 + estimateLines(info.Info);
                          if (remainingLines >= infoLines) {
                            visibleNonUrlInfos.push(info);
                            remainingLines -= infoLines;
                          } else {
                            // If we can't fit the whole info item, truncate the text
                            if (remainingLines >= 2) {
                              const maxChars = (remainingLines - 1) * 80;
                              const truncatedInfo = {
                                ...info,
                                Info:
                                  info.Info.length > maxChars
                                    ? info.Info.substring(0, maxChars) + "..."
                                    : info.Info,
                              };
                              visibleNonUrlInfos.push(truncatedInfo);
                              remainingLines = 0;
                            }
                            break;
                          }
                        }

                        // Then add URL infos if there's space
                        for (const info of urlInfos) {
                          if (remainingLines >= 1) {
                            visibleUrlInfos.push(info);
                            remainingLines -= 1;
                          } else {
                            break;
                          }
                        }
                      }

                      return (
                        <>
                          {visibleNonUrlInfos.map((info: any, idx: number) => (
                            <div key={idx}>
                              <p className="font-semibold text-brand-black">
                                {info.DisplayName}
                              </p>
                              <p className="text-brand-info">{info.Info}</p>
                            </div>
                          ))}
                          {/* Render URL infos at the bottom */}
                          {visibleUrlInfos.length > 0 && (
                            <div className="mt-6 flex flex-row max-md:flex-col gap-2">
                              {visibleUrlInfos.map((info: any, idx: number) => (
                                <div key={idx}>
                                  <a
                                    href={info.Info}
                                    target="_blank"
                                    className="text-brand-grey underline break-all bg-transparent border-none p-0 cursor-pointer"
                                  >
                                    {info.DisplayName}
                                  </a>
                                </div>
                              ))}
                            </div>
                          )}
                          {/* Read More / Read Less Button */}
                          {shouldShowReadMore && (
                            <div className="mt-4 flex justify-end">
                              <button
                                onClick={() => setShowAllInfo(!showAllInfo)}
                                className="flex items-end gap-2 text-brand-info hover:text-brand-info/80 transition-colors text-sm font-medium"
                              >
                                {showAllInfo ? (
                                  <>
                                    Read Less
                                    <ChevronDown className="w-4 h-4 rotate-180" />
                                  </>
                                ) : (
                                  <>
                                    Read More
                                    <ChevronDown className="w-4 h-4" />
                                  </>
                                )}
                              </button>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Booking Details & Contact Support */}
          <div className="lg:col-span-1 space-y-6 lg:sticky lg:top-6 self-start h-fit">
            {/* Booking Details */}
            <div className="bg-white rounded-xl border border-gray-200 p-6 w-full">
              <div className="flex items-center justify-between mb-2">
                <span className="font-bold text-lg text-[#18196A]">
                  Booking Details
                </span>
                <span className="bg-[#19C37D] text-white text-xs font-semibold px-4 py-1 rounded-full">
                  {bookingDetails?.travelers[0]?.status}
                </span>
              </div>
              <div className="flex flex-col gap-1 mb-4 mt-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400 font-medium">
                    Booking Reference :
                  </span>
                  <span className="text-[#18196A] text-sm font-semibold">
                    {bookingDetails?.tf_reference}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400 font-medium">
                    Passengers :
                  </span>
                  <span className="text-[#18196A] text-sm font-semibold">
                    {bookingDetails?.travelers.length || 0}
                  </span>
                </div>
              </div>
              {/* {props.showDownloadTicket && ( */}
              <Button
                variant="default"
                onClick={handleDownloadPDF}
                className="w-1/2 bg-[#18196A] text-white font-semibold rounded-lg py-2 mt-2 hover:bg-[#18196A]/90 hover:text-white"
              >
                Download PDF
              </Button>
              {/* )} */}
            </div>

            {/* Contact Support */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 item-center text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Contact Support
              </h3>
              <div className="space-y-3 px-8">
                {/* {props.contactSupport?.chat && ( */}
                <Button
                  variant="outline"
                  className="w-full py-[2px] font-semibold text-[#1E1E76] border-[#1E1E76] hover:bg-[#1E1E76] hover:text-white border-2 rounded-lg group"
                  onClick={handleEmailClick}
                >
                  <img
                    src="images/dashboard/iconImage/chat-ai-line.svg"
                    alt=""
                    className="transition-all filter group-hover:invert group-hover:brightness-0 group-hover:contrast-200"
                  />
                  Chat Support
                </Button>
                {/* )} */}
                {/* {props.contactSupport?.email && ( */}
                <Button
                  variant="outline"
                  className="w-full font-semibold text-[#1E1E76] border-[#1E1E76] hover:bg-[#1E1E76] hover:text-white border-2 rounded-lg"
                  onClick={handleWhatsAppClick}
                >
                  <img
                    src="images/dashboard/iconImage/mail-ai-line.svg"
                    alt=""
                    className="transition-all filter group-hover:invert group-hover:brightness-0 group-hover:contrast-200"
                  />
                  Email Support
                </Button>
                {/* )} */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightInformation;
